'use client';

import React, { useState, useRef, useCallback } from 'react';
import { UploadedPhoto } from '@/types/template';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';

interface ImageUploadProps {
  onPhotosUploaded: (photos: UploadedPhoto[]) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onPhotosUploaded,
  maxFiles = 20,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
}) => {
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const createPhotoFromFile = useCallback(async (file: File): Promise<UploadedPhoto> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      const img = new Image();
      
      reader.onload = (e) => {
        const url = e.target?.result as string;
        img.onload = () => {
          const photo: UploadedPhoto = {
            id: `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            file,
            url,
            width: img.width,
            height: img.height
          };
          resolve(photo);
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = url;
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  }, []);

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported. Please use JPEG, PNG, or WebP images.`;
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return 'File size must be less than 10MB.';
    }
    
    return null;
  };

  const processFiles = async (files: FileList) => {
    setIsUploading(true);
    const newPhotos: UploadedPhoto[] = [];
    const errors: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (uploadedPhotos.length + newPhotos.length >= maxFiles) {
        errors.push(`Maximum ${maxFiles} files allowed. Some files were skipped.`);
        break;
      }

      const validationError = validateFile(file);
      if (validationError) {
        errors.push(`${file.name}: ${validationError}`);
        continue;
      }

      try {
        const photo = await createPhotoFromFile(file);
        newPhotos.push(photo);
      } catch (error) {
        errors.push(`${file.name}: Failed to process image`);
      }
    }

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    const updatedPhotos = [...uploadedPhotos, ...newPhotos];
    setUploadedPhotos(updatedPhotos);
    onPhotosUploaded(updatedPhotos);
    setIsUploading(false);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  const removePhoto = (photoId: string) => {
    const updatedPhotos = uploadedPhotos.filter(photo => photo.id !== photoId);
    setUploadedPhotos(updatedPhotos);
    onPhotosUploaded(updatedPhotos);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full">
      {/* Upload Area */}
      <Card className={`mb-6 transition-all duration-200 ${
        isDragOver ? 'border-blue-500 border-2 bg-blue-50' : 'border-dashed border-2 border-gray-300'
      }`}>
        <CardContent className="p-8">
          <div
            className="text-center cursor-pointer"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={openFileDialog}
          >
            <div className="text-6xl mb-4">
              {isUploading ? '⏳' : isDragOver ? '📤' : '📷'}
            </div>
            <h3 className="text-xl font-semibold mb-2">
              {isUploading ? 'Processing images...' : 'Upload Your Photos'}
            </h3>
            <p className="text-gray-600 mb-4">
              {isDragOver 
                ? 'Drop your images here' 
                : 'Drag and drop images here, or click to select files'
              }
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <p>Supported formats: JPEG, PNG, WebP</p>
              <p>Maximum file size: 10MB</p>
              <p>Maximum {maxFiles} files</p>
            </div>
            {!isUploading && (
              <Button className="mt-4" disabled={isUploading}>
                Choose Files
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Photo Grid */}
      {uploadedPhotos.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                Uploaded Photos ({uploadedPhotos.length})
              </h3>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setUploadedPhotos([]);
                  onPhotosUploaded([]);
                }}
              >
                Clear All
              </Button>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {uploadedPhotos.map((photo) => (
                <div key={photo.id} className="relative group">
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={photo.url}
                      alt="Uploaded photo"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <button
                    onClick={() => removePhoto(photo.id)}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                  <div className="mt-1 text-xs text-gray-500 truncate">
                    {photo.file.name}
                  </div>
                  <div className="text-xs text-gray-400">
                    {photo.width} × {photo.height}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ImageUpload;
